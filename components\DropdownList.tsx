'use client'

import Image from "next/image"
import { useState } from "react"



const DropdownList = () => {
const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <div className="cursor-pointer onClick={() => setIsOpen(!isOpen)}">
    <div className="filter-trigger">
    <figure  >
        <Image src="/assets/icons/hamburger.svg"
        alt="menu" width={14} height={14}
        />
        Most react
    </figure>
    <Image src="/assets/icons/arrow-down.svg" alt="arrow-dwon" width={20} height={20}
    />
    </div>
      </div>
      {isOpen && (
        <ul className="dropdwon">
            {['Most react' , 'Most liked'].map((option) => (
                <li key={option}>
                    {option}
                </li>
            ))}
        </ul>
      )}

    </div>
  )
}

export default DropdownList
