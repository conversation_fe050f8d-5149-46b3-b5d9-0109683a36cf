import Image from 'next/image'
import React from 'react'

type FileInputProps = {
  id: string;
  label: string;
  accept?: string;
  file?: File;
  previweUrl?: string;
  inputRef?: React.RefObject<HTMLInputElement>;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  onReset?: () => void;
  type?: string;
};

const FileInput = ({id, label, accept, file ,previweUrl, inputRef,onChange, onReset ,type}: FileInputProps) => {
  return (
    <section className='file-input'>
      <label htmlFor={id}>{label}</label>

      <input
      type='file'
      id={id}
      accept={accept}
      ref={inputRef}
      hidden
      onChange={onChange}
      />

    {!previweUrl ? (
      <figure onClick={() => inputRef.current?.click()}>
        <Image src="/assets/icons/upload.svg" alt="upload" width={24} height={24} />
        <p>Click to upload your {id}</p>
      </figure>
    ): (
      <div>
        {type === 'video' ?
        <video src={previweUrl} controls />
        : <Image src={previweUrl} alt="image" fill
 />        }

 <button type='button' onClick={onReset}>
  <Image  src="/assets/icons/close.svg" alt='close' width={16} height={16} />
 </button>
 <p>{file?.name} </p>
      </div>
    )}

    </section>
  )
}

export default FileInput
