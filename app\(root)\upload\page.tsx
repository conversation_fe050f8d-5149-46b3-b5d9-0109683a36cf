"use client"
import FileInput from '@/components/FileInput'
import <PERSON><PERSON>ield from '@/components/FormField'
import { MAX_THUMBNAIL_SIZE, MAX_VIDEO_SIZE } from '@/constants'
import { getThumbnailUploadUrl, getVideoUploadUrl, saveVideoDetails } from '@/lib/actions/video'
import { useFileInput } from '@/lib/hooks/useFileInput'

import { useRouter } from 'next/navigation'
import { ChangeEvent, FormEvent, useEffect, useState } from 'react'

const uploadFileToBunny = (file: File , uploadUrl: string, accessKey: string): Promise<void> => {
  return fetch(uploadUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': file.type,
      AccessKey: accessKey,
    },
    body: file,
  }).then((response) => {
    if(!response.ok) throw new Error("upload failed")
  })
}

const page = () => {
  const router = useRouter();
  const [isSubmitting , setIsSubmitting] = useState(false)
  const [videoDuration , setVideoDuration] = useState(0)

  
  const [formData , setFormData] =useState({
    title: '',
    description: '',
    visibility: 'public',
  })
  const video = useFileInput(MAX_VIDEO_SIZE)
  useEffect(() => {
    if(video.duration !== null && video.duration > 0 )
      setVideoDuration(video.duration)
  }, [video.duration])

  const thumbnail = useFileInput(MAX_THUMBNAIL_SIZE)


    const [error , setError] =useState('')

    const handleInputChange = (e:ChangeEvent<HTMLInputElement>) => {
      const {name , value} = e.target;

      setFormData((prevState) => ({
        ...prevState , [name]: value
      }))
    }

    const handleSubmit = async (e: FormEvent) => {
      e.preventDefault()

      setIsSubmitting(true)

      try {
        setError(''); // Clear previous errors

        if(!video.file || !thumbnail.file) {
          setError('Please upload both video and thumbnail files');
          return
        }
        if(!formData.title.trim() || !formData.description.trim()){
          setError('Please fill in all required fields (title and description)')
          return
        }

        const {
          videoId,
          uploadUrl: videoUploadUrl,
          accessKey: videoAccessKey
        } = await getVideoUploadUrl()
        
        if(!videoUploadUrl || !videoAccessKey) throw new Error('Failed to get video upload credetial')

          // upload the video to bunny
          await uploadFileToBunny(video.file, videoUploadUrl, videoAccessKey);

        //upload the thumbnail to bd
        const {
          uploadUrl: thumbnailUploadUrl,
          accessKey: thumbnailAccessKey,
          cdnUrl: thumbnailCdnUrl,
        } = await getThumbnailUploadUrl(videoId)

        if(!thumbnailUploadUrl || !thumbnailCdnUrl) throw new Error('Failed to get video thumbnail credetial')

        // attach thumbnail

        await uploadFileToBunny(thumbnail.file, thumbnailUploadUrl , thumbnailAccessKey)

        //create a new DB entry for  the video details (urls,data)
        await saveVideoDetails({
          videoId,
          thumbnailUrl: thumbnailCdnUrl,
          ...formData,
          duration: videoDuration
        })

        router.push(`/video/${videoId}`)


      } catch (error) {
        console.error('Error submitting form:', error)
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred during upload';
        setError(errorMessage);
      } finally {
        setIsSubmitting(false)
      }
    }


  return (
    <div className='wrapper-md upload-page'>
        <h1>Upload a video</h1>
      

        {error && <div className='error-field'
        >{error}</div>}

        <form className='rounded-20 shadow-10 gap-6 w-full flex flex-col px-5 py-7.5' onSubmit={handleSubmit} >
          <FormField
          id="title"
          label= "Title"
          placeholder="Enter a clean and concise video title"
          value={formData.title}
          onChange={handleInputChange}
          />

          <FormField
          id="description"
          label= "Description"
          placeholder="Describe what this video is about"
          value={formData.description}
          onChange={handleInputChange}
          />

          <FileInput
          id="video"
          label="Video"
          accept="video/*"
          file={video.file ?? undefined}
          previewUrl={video.previewUrl}
          inputRef={video.inputRef}
          onChange={video.handleFileChange}
          onReset={video.resetFile}
          type="video"
          />

          <FileInput
          id="thumbnail"
          label="Thumbnail"
          accept="image/*"
          file={thumbnail.file ?? undefined}
          previewUrl={thumbnail.previewUrl}
          inputRef={thumbnail.inputRef}
          onChange={thumbnail.handleFileChange}
          onReset={thumbnail.resetFile}
          type="image"
          />

          <FormField
            id="visibility"
            label="Visibility"
            value={formData.visibility}
            as='select'
            options={[
              {value: 'public' , label: 'Public'},
              {value: 'private' , label: 'Private'}
            ]}
            onChange={handleInputChange}
          />

          <button
            type="submit"
            className="submit-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Uploading...' : 'Upload Video'}
          </button>

        </form>

      <button className='submit-button' type='submit' disabled={isSubmitting}>
        {isSubmitting ? 'Uploading...' : 'Upload video'}
         </button>
    </div>
  )
}

export default page
