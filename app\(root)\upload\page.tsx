"use client"
import FileInput from '@/components/FileInput'
import <PERSON><PERSON><PERSON> from '@/components/FormField'
import { MAX_THUMBNAIL_SIZE, MAX_VIDEO_SIZE } from '@/constants'
import { getThumbnailUplaodUrl, getVideoUploadUrl, saveVideoDetails } from '@/lib/actions/video'
import { useFileInput } from '@/lib/hooks/useFileInput'
import { duration } from 'drizzle-orm/gel-core'
import { useRouter } from 'next/navigation'
import { ChangeEvent, FormEvent, useEffect, useState } from 'react'

const uploadFileToBunny = (file: File , uploadUrl: string, accessKey: string): Promise<void> => {
  return fetch(uploadUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': file.type,
      AccessKey: accessKey,
    },
    body: file,
  }).then((response) => {
    if(!response.ok) throw new Error("upload failed")
  })
}

const page = () => {
  const router = useRouter();
  const [isSubmitting , setIsSubmitting] = useState(false)
  const [videoDuration , setVideoDuration] = useState(0)

  
  const [formData , setFormData] =useState({
    title: '',
    description: '',
    visibility: 'public',
  })
  const video = useFileInput(MAX_VIDEO_SIZE)
  useEffect(() => {
    if(video.duration !== null || 0 ) 
      setVideoDuration(video.duration)
  }, [video,duration])

  const thumbnail = useFileInput(MAX_THUMBNAIL_SIZE)


    const [error , setError] =useState('')

    const handleInputChange = (e:ChangeEvent<HTMLInputElement>) => {
      const {name , value} = e.target;

      setFormData((prevState) => ({
        ...prevState , [name]: value
      }))
    }

    const handleSubmit = async (e: FormEvent) => {
      e.preventDefault()

      setIsSubmitting(true)

      try {
        if(!video.file || !thumbnail.file) {
          setError('Please upload video and thumbnail');
          return
        }
        if(!formData.title || !formData.description){
          setError('Please fill in all the detail')
          return
        }

        const {
          videoId,
          uploadUrl: videoUploadUrl,
          accessKey: videoAccessKey
        } = await getVideoUploadUrl()
        
        if(!videoUploadUrl || !videoAccessKey) throw new Error('Failed to get video upload credetial')

          // upload the video to bunny
          await uploadFileToBunny(video.file, videoUploadUrl, videoAccessKey);

        //upload the thumbnail to bd 
        const {
          uploadUrl: thumbnailUploadUrl,
          accessKey: thumbnailAccessKey,
          cdnUrl: thumbnailCdnUrl,
        } = await getThumbnailUplaodUrl(videoId)

        if(!thumbnailUploadUrl || !thumbnailCdnUrl) throw new Error('Failed to get video thumbnail credetial')

        // attach thumbnail

        await uploadFileToBunny(thumbnail.file, thumbnailUploadUrl , thumbnailAccessKey)

        //create a new DB entry for  the video details (urls,data)
        await saveVideoDetails({
          videoId,
          thumbnailUrl: thumbnailCdnUrl,
          ...formData,
          duration: videoDuration
        })

        router.push(`/video/${videoId}`)


      } catch (error) {
        console.log( 'Error submitting from:' , error)
      } finally {
        setIsSubmitting(false)
      }
    }


  return (
    <div className='wrapper-md upload-page'>
        <h1>Upload a video</h1>
      

        {error && <div className='error-field'
        >{error}</div>}

        <form className='rounded-20 shadow-10 gap-6 w-full flex flex-col px-5 py-7.5' onSubmit={handleSubmit} >
          <FormField
          id="title"
          label= "Tilte"
          placeholder="Enter a clean and concise video title"
          value={formData.title}
          onChange={handleInputChange}
          />

          <FormField
          id="description"
          label= "Description"
          placeholder="Describe what this videdo is about"
          value={formData.description}
          onChange={handleInputChange}
          />

        </form>
      
      <FileInput
      id="video"
      label="Video"
      accept="video/*"
      file={video.file ?? undefined}
      previewUrl={video.previewUrl}
      inputRef={video.inputRef}
      onchange={video.handleFileChange}
      onReset={video.resetFile}
      type="video"
      />
      <FileInput
      id="thumbnail"
      label="Thumbnail"
      accept="image/*"
      file={thumbnail.file ?? undefined}
      previewUrl={thumbnail.previewUrl}
      inputRef={thumbnail.inputRef}
      onchange={thumbnail.handleFileChange}
      onReset={thumbnail.resetFile}
      type="video"
      />


      <FormField
        id="visibility"
        label="Visibility"
        value={formData.visibility}
        as='select'
        options={[
          {value: 'public' , label: 'Public'},
          {value: 'private' , label: 'Private'}
        ]}
        onChange={handleInputChange}
      />

      <button className='submit-button' type='submit' disabled={isSubmitting}>
        {isSubmitting ? 'Uploading...' : 'Upload video'}
         </button>
    </div>
  )
}

export default page
