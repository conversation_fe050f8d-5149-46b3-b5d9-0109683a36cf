{"name": "screenrec", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@arcjet/inspect": "^1.0.0-beta.9", "@arcjet/next": "^1.0.0-beta.9", "@better-auth/cli": "^1.2.8", "@xata.io/client": "^0.0.0-next.v93343b9646f57a1e5c51c35eccf0767c2bb80baa", "better-auth": "^1.2.8", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.0", "next": "15.3.2", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-core": "^6.26.3", "babel-loader": "^10.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}