import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { satoshi } from "@/fonts/font";


const geistKarla = <PERSON><PERSON>({
  variable: "--font-geist-<PERSON><PERSON>",
  subsets: ["latin"],
});



export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistKarla.variable} ${satoshi.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
